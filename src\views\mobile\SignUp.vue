<template>
  <div class="signup-container">
    <!-- 美化的导航栏 -->
    <div class="modern-nav-bar">
      <div class="nav-content">
        <div class="nav-left" @click="onBack">
          <i class="el-icon-arrow-left"></i>
          <span>返回</span>
        </div>
        <div class="nav-title">
          <h2>报名信息</h2>
          <p>Activity Registration</p>
        </div>
        <div class="nav-right">
          <!-- 显示组队模式信息 -->
          <div class="team-mode-indicator" v-if="$route.query.isTeam && Number($route.query.isTeam) > 0">
            <span class="mode-text">{{ getTeamModeText() }}</span>
          </div>
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="nav-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
      </div>
    </div>

    <div class="content">
      <van-loading
          type="spinner"
          class="spinner"
          color="#2563eb"
          v-if="loading"
        />
      <FormCreate
        v-else
        :formFields="signUpFormList"
        :formFieldsValue="formFieldValue"
        :columns="captainList"
        :isDisabled='checked'
        :team="JSON.parse(this.$route.query.team)"
        :isTeam='Number(this.$route.query.isTeam)'
        :bringRelativesNum="this.$route.query.bringRelativesNum"
        @submit="emitSubmit">
        <template v-if="$route.query.agreement !==''">
        <van-checkbox class="footer" v-model="checked" shape="square" checked-color="#2563eb">是否同意
            <a :href="agreementUrl" @click="stopBubble" :download="agreementUrl">协议</a>
          </van-checkbox>
        </template>
      </FormCreate>
    </div>
  </div>
</template>

<script>
import FormCreate from '../../components/FormCreate';
import { getSignUpFieldFromMobile, signUpFromMobile } from '../../api/signUp/index';
import requestMixin from '../../mixin/requestMixin';
import { getTeamListFromMobile } from '../../api/team/index';
export default {
  components: {
    FormCreate
  },
  mixins: [requestMixin],
  data() {
    return {
      name: '',
      sex: '1',
      isFamily: false,
      checked: false,
      // 报名信息字段
      signUpFormList: [],
      captainList: [],
      formFieldValue: {},
      agreementUrl: '',
      loading: false,
      timer: null
    };
  },
  created() {
    if (!this.$route.query.actId && !this.$route.query.teamMax) {
      this.$router.push('/list');
    } else {
      this.$route.query.agreement === '' && (this.checked = true);
      this.$route.query.agreement !== '' && this.formatUrl(this.$route.query.agreement);
      this.getSignUpFieldFromMobile();
      this.getTeamListFromMobile();
    }
  },
  beforeDestroy() {
    if (this.timer) {
      this.timer = null;
    }
  },
  methods: {
    // 获取组队模式文本
    getTeamModeText() {
      const isTeam = Number(this.$route.query.isTeam);
      switch (isTeam) {
        case 1:
          return '队长组队';
        case 2:
          return '部门组队';
        case 3:
          return '自由组队';
        default:
          return '个人报名';
      }
    },
    formatUrl(url) {
      const index = url.lastIndexOf('/');
      const fileName = url.slice(index + 1, url.length);
      this.agreementUrl = `api/file/public/download?filePath=${url}&fileName=${fileName}&from=mobile`;
    },
    emitSubmit(val, isTeamLeader) {
      const info = {};
      info.carrier = Number(val['亲属人数']);
      if (this.$route.query.isTeam === '0') {
        info.applyInfo = JSON.stringify(val);
        info.actId = Number(this.$route.query.actId);
      } else if (isTeamLeader === '1') {
        // 自由组队模式下并选择成为队长
        info.applyInfo = JSON.stringify(val);
        info.isTeamLeader = 1;
        info.actId = Number(this.$route.query.actId);
      } else {
        try {
          this.captainList.forEach(item => {
            if (item.teamName === val['队伍']) {
              info.applyInfo = JSON.stringify(val);
              info.isTeamLeader = 0;
              info.teamId = item.id;
              info.actId = Number(this.$route.query.actId);
              throw new Error('stop');
            }
          });
        } catch (e) {}
      }
      this.loading = true;
      signUpFromMobile(info).then(res => {
        const { message, status } = res.data;
        if (status === 0) {
          this.$toast.success('报名成功');
          this.$router.go(-1);
        } else if (status === -1 && message === '候补中') {
          this.$toast.fail(message);
          // this.timer = setTimeout(() => {
          this.$router.go(-1);
          // }, 1000);
        } else {
          this.$toast.fail(message);
        }
        this.loading = false;
      });
    },
    async getTeamListFromMobile() {
      const [err, res] = await this.request(getTeamListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      res.data.data.map(item => {
        item.disabled = item.number >= this.$route.query.teamMax;
        return item;
      });
      this.captainList = res.data.data;
    },
    async getSignUpFieldFromMobile() {
      const [err, res] = await this.request(getSignUpFieldFromMobile, {
        actId: this.$route.query.actId
      });
      if (err) {
        return;
      }
      // 格式[{id: xx, fieldName: "姓名", isRequired: 1, deleted: 0, actId: 35, isHide: 0},···]
      const signUpFormList = res.data.data;
      this.formFieldValue = this.getFormFieldValue();

      const { isTeam, team, isAllowedBring } = this.$route.query;

      for (let i = 0; i < signUpFormList.length; i++) {
        const item = signUpFormList[i];
        // 判断是否组队模式,不组队去掉队伍字段
        if (item.fieldName === '队伍') {
          if (Number(isTeam) === 0) {
            signUpFormList.splice(i, 1);
            i--;
          }
          if (team !== '{}') {
            this.formFieldValue[item.fieldName] = JSON.parse(team).teamName;
          }
        }

        // 判断是否允许携带亲属，不允许去掉携带亲属字段
        if (item.fieldName === '亲属人数' || item.fieldName === '亲属备注') {
          if (Number(isAllowedBring) === 0) {
            signUpFormList.splice(i, 1);
            i--;
          }
        }
      }

      this.signUpFormList = [...signUpFormList];
    },
    getFormFieldValue() {
      const value = {};
      this.signUpFormList.forEach((item, index) => {
        value[item.fieldName] = '';
      });
      value['姓名'] = localStorage.getItem('user');
      return value;
    },
    onBack() {
      this.$router.go(-1);
    },
    stopBubble(e) {
      e.stopPropagation();
    }
  }
};
</script>

<style lang="scss" scoped>
.signup-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 美化的导航栏样式
.modern-nav-bar {
  position: relative;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 20px 16px 16px;
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
  overflow: hidden;

  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;

    .nav-left {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:active {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(0.95);
      }

      i {
        font-size: 18px;
        margin-right: 6px;
      }

      span {
        font-size: 14px;
        font-weight: 500;
      }
    }

    .nav-title {
      text-align: center;
      flex: 1;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.2;
      }

      p {
        margin: 2px 0 0 0;
        font-size: 12px;
        opacity: 0.8;
        font-weight: 400;
      }
    }

    .nav-right {
      min-width: 80px;
      display: flex;
      justify-content: flex-end;

      .team-mode-indicator {
        background: rgba(255, 255, 255, 0.15);
        padding: 4px 8px;
        border-radius: 12px;
        backdrop-filter: blur(10px);

        .mode-text {
          font-size: 11px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  // 装饰性元素
  .nav-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 120px;
        height: 120px;
        top: -60px;
        right: -30px;
        animation: float 6s ease-in-out infinite;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        bottom: -40px;
        left: -20px;
        animation: float 8s ease-in-out infinite reverse;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.content {
  padding: 16px;
  margin-top: -8px;
  border-radius: 16px 16px 0 0;
  background: #f8fafc;
  min-height: calc(100vh - 100px);

  .footer {
    left: 50%;
    transform: translate(25%, 0);
    padding: 16px;
    text-align: center;
    font-size: 14px;

    :deep(.van-button) {
      margin: 0 20px;
      height: 40px;
      padding: 0 20px;
      border-radius: 8px;
    }

    :deep(.van-checkbox) {
      justify-content: center;
      margin: 20px 0;

      .van-checkbox__label {
        color: #374151;
        font-weight: 500;
      }

      a {
        color: #2563eb;
        text-decoration: none;
        font-weight: 600;
        margin-left: 4px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.spinner {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

// 响应式适配
@media (max-width: 375px) {
  .modern-nav-bar {
    padding: 16px 12px 12px;

    .nav-content {
      .nav-title {
        h2 {
          font-size: 16px;
        }

        p {
          font-size: 11px;
        }
      }

      .nav-left {
        padding: 6px 8px;

        i {
          font-size: 16px;
        }

        span {
          font-size: 13px;
        }
      }

      .nav-right {
        .team-mode-indicator {
          .mode-text {
            font-size: 10px;
          }
        }
      }
    }
  }

  .content {
    padding: 12px;
  }
}
</style>
